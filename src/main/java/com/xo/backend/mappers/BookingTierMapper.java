package com.xo.backend.mappers;

import com.xo.backend.database.entity.bookings.BookingTierEntity;
import com.xo.backend.model.dto.BookingTierDTO;
import com.xo.backend.utlis.SecurityContextUtil;
import org.apache.commons.lang3.math.NumberUtils;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import static com.xo.backend.utlis.Constants.*;

@Mapper(componentModel = "spring")
public interface BookingTierMapper {


    @Mapping(target = "availability", source = "availableQty")
    @Mapping(target = "tierName", ignore = true)
    @Mapping(target = "description", ignore = true)
    @Mapping(target = "minimumSpent", ignore = true)
    @Mapping(target = "includedConsumptionAmount", ignore = true)
    @Mapping(target = "includedConsumptionDescription", ignore = true)
    @Mapping(target = "eventId", source = "bookingTierEntity.event.id")
    @Mapping(target = "isVisible", ignore = true)
    BookingTierDTO mapToBookingTierDTO(BookingTierEntity bookingTierEntity);

    @AfterMapping
    @SuppressWarnings("java:S131")
    default void applyAttributes(BookingTierEntity bookingTierEntity, @MappingTarget BookingTierDTO.BookingTierDTOBuilder bookingTierDTOBuilder) {

        bookingTierEntity.getBookingTierAttributes().forEach(bookingTierAttributeEntity -> {
            switch (bookingTierAttributeEntity.getAttribute().getName()) {
                case TIER_NAME -> bookingTierDTOBuilder.tierName(bookingTierAttributeEntity.getAttributeValue());

                case DESCRIPTION -> bookingTierDTOBuilder.description(bookingTierAttributeEntity.getAttributeValue());

                case MINIMUM_SPENT ->
                        bookingTierDTOBuilder.minimumSpent(NumberUtils.createBigDecimal(bookingTierAttributeEntity.getAttributeValue()));

                case INCLUDED_CONSUMPTION_AMOUNT ->
                        bookingTierDTOBuilder.includedConsumptionAmount(NumberUtils.createBigDecimal(bookingTierAttributeEntity.getAttributeValue()));

                case INCLUDED_CONSUMPTION_DESCRIPTION ->
                        bookingTierDTOBuilder.includedConsumptionDescription(bookingTierAttributeEntity.getAttributeValue());
            }
        });
    }

    @AfterMapping
    default void mapAdminOnlyFields(BookingTierEntity bookingTierEntity, @MappingTarget BookingTierDTO.BookingTierDTOBuilder bookingTierDTOBuilder) {
        if (SecurityContextUtil.hasAdminOrStoreAdminPrivileges(bookingTierEntity.getEvent().getVenueId())) {
            bookingTierDTOBuilder.isVisible(bookingTierEntity.getIsVisible());
        }
    }


}
