package com.xo.backend.mappers;

import com.xo.backend.database.entity.entrypass.EntryPassEntity;
import com.xo.backend.model.dto.EntryPassDTO;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import static java.lang.Thread.yield;

@Mapper(componentModel = "spring")
public interface EntryPassMapper {

    @Mapping(source = "id", target = "entryPassId")
    @Mapping(source = "entryPassEntity.booking.ownerName", target = "bookedByFullName")
    @Mapping(source = "referenceNumber", target = "entryPassReferenceNumber")
    EntryPassDTO toEntryPassDTO(EntryPassEntity entryPassEntity);

    @AfterMapping
    default void mapAttributes(EntryPassEntity entryPassEntity, @MappingTarget EntryPassDTO.EntryPassDTOBuilder entryPassDTOBuilder) {
        entryPassEntity.getBookingTier().getBookingTierAttributes().forEach(bookingTierAttributeEntity -> {
            switch (bookingTierAttributeEntity.getAttribute().getName()) {
                case "tier-name" -> entryPassDTOBuilder.bookingTierName(bookingTierAttributeEntity.getAttributeValue());
                case "description" ->
                        entryPassDTOBuilder.entryPassDescription(bookingTierAttributeEntity.getAttributeValue());
                case "included-consumption-amount" ->
                        entryPassDTOBuilder.entryPassIncludedConsumptionAmount(bookingTierAttributeEntity.getAttributeValue());
                case "included-consumption-description" ->
                        entryPassDTOBuilder.entryPassIncludedConsumptionText(bookingTierAttributeEntity.getAttributeValue());
            }
        });
    }
}
