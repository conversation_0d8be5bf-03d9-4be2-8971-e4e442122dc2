package com.xo.backend.specs;

import com.xo.backend.database.entity.bookings.BookingEntity;
import com.xo.backend.database.entity.bookings.BookingStatus;
import org.junit.jupiter.api.Test;
import org.springframework.data.jpa.domain.Specification;

import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class BookingSpecsTest {

    @Test
    void excludeDraftStatus_shouldCreateCorrectSpecification() {
        // Arrange
        Root<BookingEntity> root = mock(Root.class);
        CriteriaQuery<?> query = mock(CriteriaQuery.class);
        CriteriaBuilder cb = mock(CriteriaBuilder.class);
        Predicate expectedPredicate = mock(Predicate.class);

        when(root.get("status")).thenReturn(mock(jakarta.persistence.criteria.Path.class));
        when(cb.notEqual(any(), eq(BookingStatus.DRAFT))).thenReturn(expectedPredicate);

        // Act
        Specification<BookingEntity> spec = BookingSpecs.excludeDraftStatus();
        Predicate result = spec.toPredicate(root, query, cb);

        // Assert
        assertNotNull(spec);
        assertEquals(expectedPredicate, result);
        verify(cb).notEqual(any(), eq(BookingStatus.DRAFT));
    }
}
